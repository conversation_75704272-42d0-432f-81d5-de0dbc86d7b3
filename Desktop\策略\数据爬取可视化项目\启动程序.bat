@echo off
chcp 936 >nul
title DongFangCaiFu Monitor System
echo ========================================
echo    DongFangCaiFu Real-time Monitor
echo ========================================
echo Version: v1.0
echo Start Time: %date% %time%
echo ========================================
echo.

REM Switch to project directory
cd /d "%~dp0"
echo Project Directory: %CD%
echo.

REM Check Python availability
echo [1/4] Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found, please install Python and add to PATH
    echo.
    echo Solutions:
    echo    1. Install Python 3.7 or higher
    echo    2. Add Python to system PATH
    echo    3. Or use Anaconda/Miniconda
    echo.
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('python --version') do set PYTHON_VERSION=%%i
echo OK: Python environment check passed: %PYTHON_VERSION%
echo.

REM Check main.py exists
echo [2/4] Checking main program file...
if not exist "main.py" (
    echo ERROR: main.py not found
    echo    Current directory: %CD%
    echo    Please run this script in correct project directory
    echo.
    pause
    exit /b 1
)
echo OK: Main program file check passed: main.py
echo.

REM Check dependencies
echo [3/4] Checking Python dependencies...
python -c "import PyQt5; import requests; print('OK: Core dependencies check passed')" 2>nul
if errorlevel 1 (
    echo WARNING: Some dependencies may not be installed
    echo    If program fails to start, run:
    echo    pip install PyQt5 requests selenium beautifulsoup4 pandas
    echo.
) else (
    echo OK: Core dependencies check passed
)
echo.

REM Start program
echo [4/4] Starting program...
echo ========================================
echo Starting DongFangCaiFu Monitor System...
echo ========================================
echo.

REM Run program
python main.py

REM Check program result
echo.
echo ========================================
if errorlevel 1 (
    echo ERROR: Program failed with error code: %errorlevel%
    echo ========================================
    echo.
    echo Solutions:
    echo    1. Check if required Python packages are installed
    echo    2. Run: pip install PyQt5 requests selenium beautifulsoup4 pandas
    echo    3. Check network connection
    echo    4. Check error messages above
    echo    5. Try running as administrator
    echo.
    echo For technical support, please save the error information above
    echo.
    pause
) else (
    echo OK: Program exited normally
    echo ========================================
)

echo.
echo Thank you for using DongFangCaiFu Monitor System!
echo Press any key to close...
pause >nul
