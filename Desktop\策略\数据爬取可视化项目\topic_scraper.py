#!/usr/bin/env python3
"""
提取东方财富VIP话题榜数据 - 使用Selenium获取动态数据
"""
import re
import json
import time
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

def extract_topic_stocks():
    """提取VIP话题榜数据"""
    
    print("正在提取东方财富VIP话题榜数据...")
    
    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--headless')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--disable-logging')
    chrome_options.add_argument('--log-level=3')
    chrome_options.add_argument('--silent')
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    try:
        # 使用webdriver-manager自动管理ChromeDriver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.set_page_load_timeout(30)
        
        # 访问页面
        url = "https://vipmoney.eastmoney.com/collect/app_ranking/ranking/app.html#/stock"
        print(f"正在访问: {url}")
        driver.get(url)
        
        # 等待页面加载
        print("等待页面加载...")
        time.sleep(10)
        
        # 等待页面内容加载完成
        try:
            WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
        except:
            print("等待页面加载超时，继续执行...")
        
        # 获取页面内容
        page_source = driver.page_source
        print(f"页面内容长度: {len(page_source)}")
        
        # 保存页面源码
        with open('topic_page_source.html', 'w', encoding='utf-8') as f:
            f.write(page_source)
        print("页面源码已保存到 topic_page_source.html")
        
        # 尝试查找话题榜相关的元素
        topics = extract_topics_from_dynamic_page(driver)
        
        if not topics:
            print("未找到话题数据，尝试从页面源码提取...")
            topics = extract_from_page_source(page_source)
        
        driver.quit()
        return topics
        
    except Exception as e:
        print(f"提取失败: {e}")
        if 'driver' in locals():
            driver.quit()
        raise e

def extract_topics_from_dynamic_page(driver):
    """从动态页面中提取话题数据"""
    topics = []
    
    try:
        # 尝试查找话题相关的元素
        # 查找包含股票代码的元素
        stock_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '000') or contains(text(), '300') or contains(text(), '600') or contains(text(), '002')]")
        
        print(f"找到包含股票代码的元素: {len(stock_elements)} 个")
        
        stock_codes = set()
        for element in stock_elements:
            text = element.text
            # 提取6位数字的股票代码
            codes = re.findall(r'\b(\d{6})\b', text)
            stock_codes.update(codes)
        
        print(f"提取到股票代码: {len(stock_codes)} 个")
        
        # 为每个股票代码创建话题
        for i, code in enumerate(list(stock_codes)[:20], 1):
            # 尝试查找股票名称
            name = find_stock_name_in_page(driver, code)
            
            topic_info = {
                'rank': i,
                'topic_title': f"{name}({code})相关话题",
                'topic_content': f"关于{name}({code})的市场讨论和热点分析",
                'stock_name': name,
                'stock_code': code,
                'change_pct': 0.0,
                'view_count': '1000',
                'comment_count': '100',
                'topic_score': i
            }
            topics.append(topic_info)
            print(f"{i}. 话题: {topic_info['topic_title']}")
    
    except Exception as e:
        print(f"从动态页面提取数据失败: {e}")
    
    return topics

def find_stock_name_in_page(driver, code):
    """在页面中查找股票名称"""
    try:
        # 查找包含股票代码的元素
        elements = driver.find_elements(By.XPATH, f"//*[contains(text(), '{code}')]")
        
        for element in elements:
            text = element.text
            # 在文本中查找中文字符（股票名称）
            name_match = re.search(r'([\u4e00-\u9fa5]{2,})', text)
            if name_match:
                return name_match.group(1)
    
    except Exception as e:
        print(f"查找股票名称失败: {e}")
    
    return f"股票{code}"

def extract_from_page_source(page_source):
    """从页面源码中提取数据"""
    topics = []
    
    try:
        # 查找股票代码
        stock_codes = re.findall(r'\b(\d{6})\b', page_source)
        
        if stock_codes:
            unique_codes = list(set(stock_codes))
            print(f"从源码中找到股票代码: {len(unique_codes)} 个")
            
            for i, code in enumerate(unique_codes[:20], 1):
                # 查找股票名称
                name = find_stock_name_in_source(page_source, code)
                
                topic_info = {
                    'rank': i,
                    'topic_title': f"{name}({code})相关话题",
                    'topic_content': f"关于{name}({code})的市场讨论和热点分析",
                    'stock_name': name,
                    'stock_code': code,
                    'change_pct': 0.0,
                    'view_count': '1000',
                    'comment_count': '100',
                    'topic_score': i
                }
                topics.append(topic_info)
                print(f"{i}. 话题: {topic_info['topic_title']}")
    
    except Exception as e:
        print(f"从页面源码提取数据失败: {e}")
    
    return topics

def find_stock_name_in_source(page_source, code):
    """在页面源码中查找股票名称"""
    try:
        # 查找包含股票代码的文本
        pattern = rf'([^<>]*{code}[^<>]*)'
        matches = re.findall(pattern, page_source)
        
        for match in matches:
            # 查找中文字符（股票名称）
            name_match = re.search(r'([\u4e00-\u9fa5]{2,})', match)
            if name_match:
                return name_match.group(1)
    
    except Exception as e:
        print(f"在源码中查找股票名称失败: {e}")
    
    return f"股票{code}"

def main():
    """主函数"""
    print("=" * 50)
    print("东方财富话题榜数据提取工具")
    print("=" * 50)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        topics = extract_topic_stocks()
        
        if topics and len(topics) > 0:
            print(f"\n=== 提取结果 ===")
            print(f"总共提取到 {len(topics)} 个话题")
            
            # 保存到JSON文件
            with open('topic_stocks.json', 'w', encoding='utf-8') as f:
                json.dump(topics, f, ensure_ascii=False, indent=2)
            print(f"数据已保存到 topic_stocks.json，共{len(topics)}个话题")
            
            print(f"\n完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("=" * 50)
        else:
            print("错误：未能提取到任何话题数据")
            import sys
            sys.exit(1)
    
    except Exception as e:
        print(f"程序执行失败: {e}")
        import sys
        sys.exit(1)

if __name__ == "__main__":
    main() 